package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

// Create implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) Create(ctx context.Context, productionFlow model.ProductionFlow) error {
	return pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO production_flows (id, code, name, product_id)
			VALUES ($1, $2, $3, $4)
		`

		_, err := conn.Exec(ctx, query, productionFlow.ID, productionFlow.Code, productionFlow.Name, productionFlow.ProductID)
		if err != nil {
			return utils.InternalErrorf("Failed to create production flow", err, nil)
		}

		return nil
	})
}

// CreateWithActivities implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) CreateWithActivities(ctx context.Context, productionFlow model.ProductionFlow, activities []model.Activity) error {
	return pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Start a transaction
		tx, err := conn.Begin(ctx)
		if err != nil {
			return utils.InternalErrorf("Failed to start transaction", err, nil)
		}
		defer tx.Rollback(ctx)

		// Create the production flow
		productionFlowQuery := `
			INSERT INTO production_flows (id, code, name, product_id)
			VALUES ($1, $2, $3, $4)
		`

		_, err = tx.Exec(ctx, productionFlowQuery, productionFlow.ID, productionFlow.Code, productionFlow.Name, productionFlow.ProductID)
		if err != nil {
			return utils.InternalErrorf("Failed to create production flow", err, nil)
		}

		// Create all activities
		for _, activity := range activities {
			activityQuery := `
				INSERT INTO activities (id, production_flow_id, work_area_id, operation_id, index_number)
				VALUES ($1, $2, $3, $4, $5)
			`

			_, err = tx.Exec(ctx, activityQuery,
				activity.ID,
				activity.ProductionFlowID,
				activity.WorkAreaID,
				activity.OperationID,
				activity.IndexNumber,
			)
			if err != nil {
				return utils.InternalErrorf("Failed to create activity", err, nil)
			}
		}

		// Commit the transaction
		err = tx.Commit(ctx)
		if err != nil {
			return utils.InternalErrorf("Failed to commit transaction", err, nil)
		}

		return nil
	})
}

// CreateActivity implements ProductionFlowPostgreRepo.
func (p *productionFlowPostgreRepo) CreateActivity(ctx context.Context, activity model.Activity) error {
	return pg.ExecuteInSchema(ctx, p.db, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO activities (id, production_flow_id, work_area_id, operation_id, index_number)
			VALUES ($1, $2, $3, $4, $5)
		`

		_, err := conn.Exec(ctx, query,
			activity.ID,
			activity.ProductionFlowID,
			activity.WorkAreaID,
			activity.OperationID,
			activity.IndexNumber,
		)
		if err != nil {
			return utils.InternalErrorf("Failed to create activity", err, nil)
		}

		return nil
	})
}
