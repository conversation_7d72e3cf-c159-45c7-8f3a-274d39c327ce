package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.ProductionFlowUsecase.
func (p *productionFlowUsecase) Create(ctx context.Context, productionFlow model.ProductionFlowCreate) (string, error) {
	// Check if code already exists
	codeExists, err := p.repo.CountByProp(ctx, "code", productionFlow.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if production flow code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.ProductionFlowConflictCodef("Production flow code already exists", nil, nil)
	}

	// Check if name already exists
	nameExists, err := p.repo.CountByProp(ctx, "name", productionFlow.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if production flow name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.ProductionFlowConflictNamef("Production flow name already exists", nil, nil)
	}

	newProductionFlow := model.ProductionFlow{
		ID:        utils.UniqueId(),
		Code:      productionFlow.Code,
		Name:      productionFlow.Name,
		ProductID: productionFlow.ProductID,
	}

	err = p.repo.Create(ctx, newProductionFlow)
	if err != nil {
		return "", err
	}

	return newProductionFlow.ID, nil
}
