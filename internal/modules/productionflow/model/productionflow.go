package model

import "time"

type ProductionFlow struct {
	ID        string
	Code      string
	Name      string
	ProductID *string
	CreatedAt *time.Time
	UpdatedAt *time.Time
	DeletedAt *time.Time
}

type ProductionFlowCreate struct {
	Code      string
	Name      string
	ProductID *string
}

type ProductionFlowUpdate struct {
	ID        string
	Code      string
	Name      string
	ProductID *string
}

type Activity struct {
	ID               string
	ProductionFlowID string
	WorkAreaID       string
	OperationID      string
	IndexNumber      int
	CreatedAt        *time.Time
	UpdatedAt        *time.Time
	DeletedAt        *time.Time
}

type ActivityCreate struct {
	ProductionFlowID string
	WorkAreaID       string
	OperationID      string
	IndexNumber      int
}

type ActivityCreateRequest struct {
	WorkAreaID  string `json:"work_area_id" validate:"required"`
	OperationID string `json:"operation_id" validate:"required"`
	IndexNumber int    `json:"index_number" validate:"required,min=1"`
}

type ProductionFlowCreateWithActivities struct {
	Code       string                  `json:"code" validate:"required,min=2,max=50"`
	Name       string                  `json:"name" validate:"required,min=2,max=100"`
	ProductID  *string                 `json:"product_id" validate:"required"`
	Activities []ActivityCreateRequest `json:"activities" validate:"required,min=1,dive"`
}

type ProductionFlowWithActivities struct {
	ProductionFlow ProductionFlow
	Activities     []ActivityDetail
}

type ActivityDetail struct {
	Activity  Activity
	WorkArea  WorkAreaInfo
	Operation OperationInfo
}

type WorkAreaInfo struct {
	ID   string
	Code string
	Name string
}

type OperationInfo struct {
	ID   string
	Code string
	Name string
}
