package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
)

// Request DTOs
type productionFlowCreate struct {
	Code      string  `json:"code" validate:"required,min=2,max=50"`
	Name      string  `json:"name" validate:"required,min=2,max=100"`
	ProductID *string `json:"product_id" validate:"required"`
}

type productionFlowUpdate struct {
	ID        string  `json:"id" validate:"required"`
	Code      string  `json:"code" validate:"required,min=2,max=50"`
	Name      string  `json:"name" validate:"required,min=2,max=100"`
	ProductID *string `json:"product_id" validate:"required"`
}

// Response DTOs
type productionFlowResponse struct {
	ID        string     `json:"id"`
	Code      string     `json:"code"`
	Name      string     `json:"name"`
	ProductID *string    `json:"product_id"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

type activityResponse struct {
	ID          string        `json:"id"`
	IndexNumber int           `json:"index_number"`
	WorkArea    workAreaInfo  `json:"work_area"`
	Operation   operationInfo `json:"operation"`
	CreatedAt   *time.Time    `json:"created_at"`
	UpdatedAt   *time.Time    `json:"updated_at"`
	DeletedAt   *time.Time    `json:"deleted_at"`
}

type workAreaInfo struct {
	ID   string `json:"id"`
	Code string `json:"code"`
	Name string `json:"name"`
}

type operationInfo struct {
	ID   string `json:"id"`
	Code string `json:"code"`
	Name string `json:"name"`
}

type productionFlowWithActivitiesResponse struct {
	ProductionFlow productionFlowResponse `json:"production_flow"`
	Activities     []activityResponse     `json:"activities"`
}

// Conversion functions
func productionFlowCreateToModel(dto productionFlowCreate) model.ProductionFlowCreate {
	return model.ProductionFlowCreate{
		Code:      dto.Code,
		Name:      dto.Name,
		ProductID: dto.ProductID,
	}
}

func productionFlowUpdateToModel(dto productionFlowUpdate) model.ProductionFlowUpdate {
	return model.ProductionFlowUpdate{
		ID:        dto.ID,
		Code:      dto.Code,
		Name:      dto.Name,
		ProductID: dto.ProductID,
	}
}

func modelToProductionFlowResponse(productionFlow model.ProductionFlow) productionFlowResponse {
	return productionFlowResponse{
		ID:        productionFlow.ID,
		Code:      productionFlow.Code,
		Name:      productionFlow.Name,
		ProductID: productionFlow.ProductID,
		CreatedAt: productionFlow.CreatedAt,
		UpdatedAt: productionFlow.UpdatedAt,
		DeletedAt: productionFlow.DeletedAt,
	}
}

func modelToProductionFlowsResponse(productionFlows []model.ProductionFlow) []productionFlowResponse {
	response := make([]productionFlowResponse, len(productionFlows))
	for i, productionFlow := range productionFlows {
		response[i] = modelToProductionFlowResponse(productionFlow)
	}
	return response
}

func modelToProductionFlowWithActivitiesResponse(data model.ProductionFlowWithActivities) productionFlowWithActivitiesResponse {
	activities := make([]activityResponse, len(data.Activities))
	for i, activity := range data.Activities {
		activities[i] = activityResponse{
			ID:          activity.Activity.ID,
			IndexNumber: activity.Activity.IndexNumber,
			WorkArea: workAreaInfo{
				ID:   activity.WorkArea.ID,
				Code: activity.WorkArea.Code,
				Name: activity.WorkArea.Name,
			},
			Operation: operationInfo{
				ID:   activity.Operation.ID,
				Code: activity.Operation.Code,
				Name: activity.Operation.Name,
			},
			CreatedAt: activity.Activity.CreatedAt,
			UpdatedAt: activity.Activity.UpdatedAt,
			DeletedAt: activity.Activity.DeletedAt,
		}
	}

	return productionFlowWithActivitiesResponse{
		ProductionFlow: modelToProductionFlowResponse(data.ProductionFlow),
		Activities:     activities,
	}
}
